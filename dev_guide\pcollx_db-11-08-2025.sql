-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Aug 10, 2025 at 11:16 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `pcollx_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `activities`
--

CREATE TABLE `activities` (
  `id` int(11) NOT NULL,
  `org_id` bigint(20) UNSIGNED DEFAULT NULL,
  `workplan_id` bigint(20) UNSIGNED DEFAULT NULL,
  `activity_type` varchar(255) DEFAULT NULL,
  `activity_name` varchar(255) NOT NULL,
  `remarks` text DEFAULT NULL,
  `date_from` date DEFAULT NULL,
  `date_to` date DEFAULT NULL,
  `status` enum('planned','active','completed','cancelled','suspended') DEFAULT NULL,
  `status_by` bigint(20) UNSIGNED DEFAULT NULL,
  `status_at` timestamp NULL DEFAULT NULL,
  `status_remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `deleted_by` int(11) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `activity_business_locations`
--

CREATE TABLE `activity_business_locations` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `activity_id` int(11) NOT NULL,
  `business_location_id` int(11) NOT NULL,
  `assigned_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `activity_price_collection_data`
--

CREATE TABLE `activity_price_collection_data` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `business_location_id` int(11) NOT NULL,
  `activity_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `remarks` text DEFAULT NULL,
  `status` enum('active','submitted','approved','redo','cancelled') NOT NULL DEFAULT 'active',
  `status_by` int(11) DEFAULT NULL,
  `status_at` datetime DEFAULT NULL,
  `status_remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `activity_users`
--

CREATE TABLE `activity_users` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `activity_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `assigned_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `business_entities`
--

CREATE TABLE `business_entities` (
  `id` int(11) NOT NULL,
  `business_name` varchar(150) NOT NULL,
  `remarks` text DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `status_by` int(11) DEFAULT NULL,
  `status_at` datetime DEFAULT NULL,
  `status_remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `deleted_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `business_locations`
--

CREATE TABLE `business_locations` (
  `id` int(11) NOT NULL,
  `business_entity_id` int(11) NOT NULL,
  `business_name` varchar(150) NOT NULL DEFAULT '',
  `remarks` text DEFAULT NULL,
  `country_id` int(11) DEFAULT NULL,
  `province_id` int(11) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `gps_coordinates` varchar(200) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `status_by` int(11) DEFAULT NULL,
  `status_at` datetime DEFAULT NULL,
  `status_remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `deleted_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `dakoii_org`
--

CREATE TABLE `dakoii_org` (
  `id` int(11) UNSIGNED NOT NULL,
  `org_code` varchar(100) NOT NULL,
  `org_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `province_id` int(11) DEFAULT NULL,
  `country_id` int(11) DEFAULT NULL,
  `logo_path` varchar(255) DEFAULT NULL,
  `is_locationlocked` tinyint(1) NOT NULL DEFAULT 0,
  `postal_address` text DEFAULT NULL,
  `phone_numbers` text DEFAULT NULL,
  `email_addresses` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `license_status` varchar(50) DEFAULT NULL,
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_deleted` tinyint(1) DEFAULT 0,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `dakoii_org`
--

INSERT INTO `dakoii_org` (`id`, `org_code`, `org_name`, `description`, `province_id`, `country_id`, `logo_path`, `is_locationlocked`, `postal_address`, `phone_numbers`, `email_addresses`, `is_active`, `license_status`, `created_by`, `updated_by`, `created_at`, `updated_at`, `is_deleted`, `deleted_at`, `deleted_by`) VALUES
(1, '14', 'East Sepik Province', 'ICCC East Sepik', 14, 1, 'public/uploads/org_logos/1754790986_dfdafb210657cce66ad5.jpg', 0, '', '', '', 1, 'active', 1, 1, '2025-08-10 11:11:16', '2025-08-10 11:56:26', 0, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `dakoii_users`
--

CREATE TABLE `dakoii_users` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 0,
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_deleted` tinyint(1) DEFAULT 0,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `dakoii_users`
--

INSERT INTO `dakoii_users` (`id`, `name`, `username`, `password`, `role`, `is_active`, `created_by`, `updated_by`, `created_at`, `updated_at`, `is_deleted`, `deleted_at`, `deleted_by`) VALUES
(1, 'Fred Kenny', 'fkenny', '$2y$10$oIenHECVxlln8.v.KfagGOkw.aQzvUIELdZK.kErdeAvnCSz1HyQS', 'super_admin', 1, 1, 1, '2025-08-09 23:36:44', '2025-08-09 23:38:14', 0, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `geo_countries`
--

CREATE TABLE `geo_countries` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(100) NOT NULL,
  `country_code` char(2) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `updated_by` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `geo_countries`
--

INSERT INTO `geo_countries` (`id`, `name`, `country_code`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
(1, 'Papua New Guinea', 'PG', '2025-08-10 10:12:23', '2025-08-10 10:12:23', 1, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `geo_districts`
--

CREATE TABLE `geo_districts` (
  `id` int(11) NOT NULL,
  `district_code` varchar(10) DEFAULT NULL,
  `province_id` int(11) DEFAULT NULL,
  `json_id` varchar(50) DEFAULT NULL,
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `updated_by` int(10) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `country_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `geo_districts`
--

INSERT INTO `geo_districts` (`id`, `district_code`, `province_id`, `json_id`, `created_by`, `updated_by`, `name`, `country_id`, `created_at`, `updated_at`) VALUES
(1, NULL, 14, NULL, NULL, NULL, 'Ambunti/Drekikier', 0, '2025-08-10 01:10:04', '2025-08-10 01:10:04'),
(2, NULL, 14, NULL, NULL, NULL, 'Angoram', 0, '2025-08-10 01:10:04', '2025-08-10 01:10:04'),
(3, NULL, 14, NULL, NULL, NULL, 'Maprik', 0, '2025-08-10 01:10:04', '2025-08-10 01:10:04'),
(4, NULL, 14, NULL, NULL, NULL, 'Wewak', 0, '2025-08-10 01:10:04', '2025-08-10 01:10:04'),
(5, NULL, 14, NULL, NULL, NULL, 'Wosera Gawi', 0, '2025-08-10 01:10:04', '2025-08-10 01:10:04'),
(6, NULL, 14, NULL, NULL, NULL, 'Yangoru Saussia', 0, '2025-08-10 01:10:04', '2025-08-10 01:10:04');

-- --------------------------------------------------------

--
-- Table structure for table `geo_provinces`
--

CREATE TABLE `geo_provinces` (
  `id` int(10) UNSIGNED NOT NULL,
  `province_code` varchar(10) NOT NULL,
  `name` varchar(100) NOT NULL,
  `country_id` int(10) UNSIGNED NOT NULL,
  `json_id` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `updated_by` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `geo_provinces`
--

INSERT INTO `geo_provinces` (`id`, `province_code`, `name`, `country_id`, `json_id`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
(1, '01', 'Western Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(2, '02', 'Gulf Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(3, '03', 'Central Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(4, '04', 'Milne Bay Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(5, '05', 'Oro Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(6, '06', 'Southern Highlands Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(7, '07', 'Southern Highlands Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(8, '08', 'Enga Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(9, '09', 'Western Highlands Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(10, '10', 'Chimbu Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(11, '11', 'Eastern Highlands Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(12, '12', 'Morobe Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(13, '13', 'Madang Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(14, '14', 'East Sepik Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(15, '15', 'West Sepik Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(16, '16', 'Manus Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(17, '17', 'New Ireland Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(18, '18', 'East New Britain Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(19, '19', 'West New Britain Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(20, '20', 'Autonomous Region of Bougainville', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(21, '21', 'National Capital District', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL),
(22, '22', 'Hela Province', 1, NULL, '2025-08-10 10:41:36', '2025-08-10 10:41:36', 1, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `goods_brands`
--

CREATE TABLE `goods_brands` (
  `id` int(11) NOT NULL,
  `goods_group_id` int(11) NOT NULL,
  `brand_name` varchar(150) NOT NULL,
  `type` enum('primary','substitute') NOT NULL DEFAULT 'primary',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `status_by` int(11) DEFAULT NULL,
  `status_at` datetime DEFAULT NULL,
  `status_remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `deleted_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `goods_groups`
--

CREATE TABLE `goods_groups` (
  `id` int(11) NOT NULL,
  `group_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `status_by` int(11) DEFAULT NULL,
  `status_at` datetime DEFAULT NULL,
  `status_remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `deleted_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `goods_items`
--

CREATE TABLE `goods_items` (
  `id` int(11) NOT NULL,
  `item` varchar(200) NOT NULL,
  `goods_group_id` int(11) NOT NULL,
  `goods_brand_id` int(11) NOT NULL,
  `remarks` text DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `status_by` int(11) DEFAULT NULL,
  `status_at` datetime DEFAULT NULL,
  `status_remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `deleted_by` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `price_data`
--

CREATE TABLE `price_data` (
  `id` int(11) NOT NULL,
  `org_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `activity_id` int(11) NOT NULL,
  `business_location_id` int(11) NOT NULL,
  `item_id` bigint(20) UNSIGNED NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `effective_date` date NOT NULL,
  `price_amount` decimal(10,2) NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) UNSIGNED NOT NULL,
  `org_id` int(11) NOT NULL,
  `sys_no` int(20) NOT NULL COMMENT 'system number',
  `name` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('user','guest') NOT NULL DEFAULT 'user',
  `is_admin` tinyint(5) NOT NULL,
  `is_supervisor` tinyint(5) NOT NULL,
  `reports_to` int(11) DEFAULT NULL,
  `position` varchar(255) DEFAULT NULL,
  `id_photo` varchar(500) DEFAULT NULL,
  `phone` varchar(200) DEFAULT NULL,
  `email` varchar(500) NOT NULL,
  `status` varchar(20) NOT NULL,
  `activation_token` varchar(255) DEFAULT NULL,
  `activation_sent_at` datetime DEFAULT NULL,
  `activated_at` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `deleted_by` int(11) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `org_id`, `sys_no`, `name`, `password`, `role`, `is_admin`, `is_supervisor`, `reports_to`, `position`, `id_photo`, `phone`, `email`, `status`, `activation_token`, `activation_sent_at`, `activated_at`, `created_by`, `updated_by`, `is_deleted`, `deleted_by`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 1, 202501, 'Aitape ITU', '$2y$10$LKnFe8ztY6Dfh1qFCDEPc.tLDr4yrO.NyVdvPsaVeHma4je2S8KHi', 'user', 1, 1, NULL, '', NULL, '+6754445566', '<EMAIL>', 'active', NULL, '2025-08-10 11:57:48', '2025-08-10 11:58:17', 1, 1, 0, NULL, NULL, '2025-08-10 11:57:48', '2025-08-10 14:45:29');

-- --------------------------------------------------------

--
-- Table structure for table `workplans`
--

CREATE TABLE `workplans` (
  `id` int(11) NOT NULL,
  `org_id` bigint(20) UNSIGNED DEFAULT NULL,
  `supervisor_id` bigint(20) UNSIGNED DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `date_from` date DEFAULT NULL,
  `date_to` date DEFAULT NULL,
  `status` enum('draft','approved','active','completed','cancelled') DEFAULT 'draft',
  `status_by` bigint(20) UNSIGNED DEFAULT NULL,
  `status_at` timestamp NULL DEFAULT NULL,
  `status_remarks` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Soft delete flag: 0=active, 1=deleted',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT 'Soft delete timestamp'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `activities`
--
ALTER TABLE `activities`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_activities_org_id` (`org_id`),
  ADD KEY `idx_activities_workplan_id` (`workplan_id`),
  ADD KEY `idx_activities_status` (`status`),
  ADD KEY `idx_activities_date_from_to` (`date_from`,`date_to`);

--
-- Indexes for table `activity_business_locations`
--
ALTER TABLE `activity_business_locations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_activity_business_locations_org_id` (`org_id`),
  ADD KEY `idx_activity_business_locations_activity_id` (`activity_id`),
  ADD KEY `idx_activity_business_locations_business_location_id` (`business_location_id`);

--
-- Indexes for table `activity_price_collection_data`
--
ALTER TABLE `activity_price_collection_data`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_activity_price_collection_org_id` (`org_id`),
  ADD KEY `idx_activity_price_collection_activity_id` (`activity_id`),
  ADD KEY `idx_activity_price_collection_user_id` (`user_id`),
  ADD KEY `idx_activity_price_collection_item_id` (`item_id`),
  ADD KEY `idx_activity_price_collection_business_location_id` (`business_location_id`);

--
-- Indexes for table `activity_users`
--
ALTER TABLE `activity_users`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_activity_users_org_id` (`org_id`),
  ADD KEY `idx_activity_users_activity_id` (`activity_id`),
  ADD KEY `idx_activity_users_user_id` (`user_id`);

--
-- Indexes for table `business_entities`
--
ALTER TABLE `business_entities`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `business_locations`
--
ALTER TABLE `business_locations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `dakoii_org`
--
ALTER TABLE `dakoii_org`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `dakoii_users`
--
ALTER TABLE `dakoii_users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `geo_countries`
--
ALTER TABLE `geo_countries`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uq_country_code` (`country_code`);

--
-- Indexes for table `geo_districts`
--
ALTER TABLE `geo_districts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_geo_districts_country_id` (`country_id`),
  ADD KEY `idx_geo_districts_province_id` (`province_id`);

--
-- Indexes for table `geo_provinces`
--
ALTER TABLE `geo_provinces`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uq_province_code` (`province_code`),
  ADD KEY `idx_country_id` (`country_id`);

--
-- Indexes for table `goods_brands`
--
ALTER TABLE `goods_brands`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `goods_groups`
--
ALTER TABLE `goods_groups`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `goods_items`
--
ALTER TABLE `goods_items`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `price_data`
--
ALTER TABLE `price_data`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_price_data_date` (`effective_date`),
  ADD KEY `idx_price_data_location` (`business_location_id`),
  ADD KEY `idx_price_data_item` (`item_id`),
  ADD KEY `idx_price_data_org_id` (`org_id`),
  ADD KEY `idx_price_data_user_id` (`user_id`),
  ADD KEY `idx_price_data_activity_id` (`activity_id`),
  ADD KEY `idx_price_data_business_location_id` (`business_location_id`),
  ADD KEY `idx_price_data_item_id` (`item_id`),
  ADD KEY `idx_price_data_effective_date` (`effective_date`),
  ADD KEY `idx_price_data_is_active` (`is_active`),
  ADD KEY `idx_price_data_deleted_at` (`deleted_at`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `workplans`
--
ALTER TABLE `workplans`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_workplans_org_id` (`org_id`),
  ADD KEY `idx_workplans_supervisor_id` (`supervisor_id`),
  ADD KEY `idx_workplans_status` (`status`),
  ADD KEY `idx_workplans_date_from_to` (`date_from`,`date_to`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activities`
--
ALTER TABLE `activities`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `activity_business_locations`
--
ALTER TABLE `activity_business_locations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `activity_price_collection_data`
--
ALTER TABLE `activity_price_collection_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `activity_users`
--
ALTER TABLE `activity_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `business_entities`
--
ALTER TABLE `business_entities`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `business_locations`
--
ALTER TABLE `business_locations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `dakoii_org`
--
ALTER TABLE `dakoii_org`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `dakoii_users`
--
ALTER TABLE `dakoii_users`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `geo_countries`
--
ALTER TABLE `geo_countries`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `geo_districts`
--
ALTER TABLE `geo_districts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `geo_provinces`
--
ALTER TABLE `geo_provinces`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `goods_brands`
--
ALTER TABLE `goods_brands`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `goods_groups`
--
ALTER TABLE `goods_groups`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `goods_items`
--
ALTER TABLE `goods_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `price_data`
--
ALTER TABLE `price_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `workplans`
--
ALTER TABLE `workplans`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
